import { Injectable } from '@angular/core';
import { Observable, Subject, map, of, take, takeUntil } from 'rxjs';
import { Instrument } from 'src/app/request-information/models';
import { InstrumentsService } from 'src/app/request-information/services';
import { API_URL } from '../constants/api-url.constants';
import { CBGetResponse, CBResponse } from '../models';
import { SchoolLocations } from 'src/app/pages/room-and-location-management/models';
import { LocationService } from 'src/app/pages/room-and-location-management/pages/location/services';
import { InstructorList } from 'src/app/schedule-introductory-lesson/models';
import { InstructorService } from 'src/app/schedule-introductory-lesson/services';
import { RevenueCategory } from 'src/app/pages/settings/pages/revenue-categories/models';
import { RevenueCategoriesService } from 'src/app/pages/settings/pages/revenue-categories/services';
import { State } from 'src/app/auth/models/user.model';
import { AddressService } from './address.service';
import { DependentInformations } from 'src/app/pages/members/pages/students/models';
import { DependentService } from 'src/app/pages/profile/services';

@Injectable({ providedIn: 'root' })
export class CommonService {
  isApiCallInProgress$: Subject<boolean> = new Subject();
  instruments!: CBResponse<Instrument>;
  locations!: CBResponse<SchoolLocations>;
  instructors!: CBResponse<InstructorList>;
  revenueCategories!: CBResponse<RevenueCategory>;
  states!: CBGetResponse<Array<State>>;
  studentList!: CBGetResponse<DependentInformations[]>;

  constructor(
    private instrumentsService: InstrumentsService,
    private readonly locationService: LocationService,
    private readonly instructorService: InstructorService,
    private readonly revenueCategoriesService: RevenueCategoriesService,
    private readonly addressService: AddressService,
    private readonly dependentService: DependentService
  ) {}

  getInstruments(): Observable<CBResponse<Instrument>> {
    if (!this.instruments?.result?.items?.length) {
      return this.instrumentsService.getList<CBResponse<Instrument>>(API_URL.crud.getAll).pipe(
        take(1),
        map(res => {
          this.instruments = res as CBResponse<Instrument>;
          return res;
        })
      );
    }
    return of(this.instruments);
  }

  getLocations(): Observable<CBResponse<SchoolLocations>> {
    if (!this.locations?.result?.items?.length) {
      return this.locationService.add({}, API_URL.crud.getAll).pipe(
        take(1),
        map(res => {
          this.locations = res as CBResponse<SchoolLocations>;
          return res;
        })
      );
    }
    return of(this.locations);
  }

  getInstructors(): Observable<CBResponse<InstructorList>> {
    if (!this.instructors?.result?.items?.length) {
      return this.instructorService.add({ page: 1 }, `${API_URL.crud.getAll}`).pipe(
        take(1),
        map(res => {
          this.instructors = res as CBResponse<InstructorList>;
          return res;
        })
      );
    }
    return of(this.instructors);
  }

  getRevenueCategories(): Observable<CBResponse<RevenueCategory>> {
    if (!this.revenueCategories?.result?.items?.length) {
      return this.revenueCategoriesService.getListWithFilters<CBResponse<RevenueCategory>>({}, `${API_URL.crud.getAll}`).pipe(
        take(1),
        map(res => {
          this.revenueCategories = res;
          return res;
        })
      );
    }
    return of(this.revenueCategories);
  }

  getStates(): Observable<CBGetResponse<Array<State>>> {
    if (!this.states?.result.length) {
      return this.addressService.getList<CBGetResponse<Array<State>>>(API_URL.address.getStates).pipe(
        take(1),
        map(res => {
          this.states = res;
          return res;
        })
      );
    }
    return of(this.states);
  }

  //Only when complete student list needs to be rendered.
  getStudentList(): Observable<CBGetResponse<DependentInformations[]>> {
    if (!this.studentList?.result?.length) {
      return this.dependentService
        .add({ instructorFilter: [], locationFilter: [] }, `${API_URL.dependentInformations.getAllStudents}`)
        .pipe(
          take(1),
          map(res => {
            this.studentList = res;
            return res;
          })
        );
    }
    return of(this.studentList);
  }
}
