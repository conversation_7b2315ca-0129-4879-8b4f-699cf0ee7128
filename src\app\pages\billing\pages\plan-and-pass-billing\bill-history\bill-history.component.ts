import { ChangeDetectorRef, Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CommonModule } from '@angular/common';
import { DirectivesModule } from 'src/app/shared/directives/directives.module';
import { CBResponse, IdNameModel } from 'src/app/shared/models';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { NgxPaginationModule } from 'ngx-pagination';
import { CommonUtils } from 'src/app/shared/utils';
import { ClassTypes } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { PlanSummaryService } from 'src/app/pages/settings/pages/plan/services';
import { PaymentService } from 'src/app/pages/schedule-classes/services';
import { PlanAndPassBillingFilters, BillStatus, BillHistoryRes } from '../../../models';
import { SharedModule } from 'src/app/shared/shared.module';
import { MatSelectModule } from '@angular/material/select';
import { FormsModule } from '@angular/forms';
import { All } from 'src/app/pages/settings/pages/plan/models';
import { AuthService } from 'src/app/auth/services';
import { ActivatedRoute, Router } from '@angular/router';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { provideNativeDateAdapter } from '@angular/material/core';
import moment from 'moment';
import { MatIconModule } from '@angular/material/icon';
import { FilterPipe, LocalDatePipe } from 'src/app/shared/pipe';
import { Account } from 'src/app/auth/models/user.model';

const DEPENDENCIES = {
  MODULES: [
    CommonModule,
    DirectivesModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    NgxPaginationModule,
    SharedModule,
    MatSelectModule,
    FormsModule,
    MatTooltipModule,
    MatDatepickerModule,
    MatIconModule
  ],
  PIPES: [LocalDatePipe, FilterPipe]
};

@Component({
  selector: 'app-bill-history',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES],
  providers: [provideNativeDateAdapter()],
  templateUrl: './bill-history.component.html',
  styleUrl: './bill-history.component.scss'
})
export class BillHistoryComponent extends BaseComponent implements OnChanges {
  @Input() currentUser$!: Account | null;
  @Input() studentList!: Array<IdNameModel>;

  selectedDependentId!: number | undefined;
  selectedUserId!: number | undefined;
  billHistoryDetails!: Array<BillHistoryRes>;
  totalCount!: number;
  searchTerm = '';
  classType = ClassTypes;
  billStatus = BillStatus;
  all = All;

  filters: PlanAndPassBillingFilters = {
    statusFilter: 0,
    startDateFilter: moment().subtract(1, 'month').format(this.constants.dateFormats.yyyy_MM_DD),
    endDateFilter: moment().format(this.constants.dateFormats.yyyy_MM_DD)
  };

  constructor(
    private readonly cdr: ChangeDetectorRef,
    protected readonly planSummaryService: PlanSummaryService,
    protected readonly schedulerService: SchedulerService,
    private readonly paymentService: PaymentService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router
  ) {
    super();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['currentUser$']?.currentValue) {
      this.currentUser = changes['currentUser$'].currentValue;
      this.getCurrentId();
    }
  }

  getCurrentId(): void {
    this.activatedRoute.queryParams.subscribe((params: any) => {
      if (params.activeTab === 'Bill History') {
        if (params.dependentId) {
          this.selectedDependentId = +params.dependentId;
          this.selectedUserId = +params.userId;
        } else {
          this.selectedDependentId = 0;
          this.selectedUserId = 0;
        }
        this.getBillHistoryDetails();
      }
    });
  }

  openStudentDetailPage(dependentId: number): void {
    if (this.currentUser?.userRoleId === this.constants.roleIds.CLIENT) {
      return;
    }
    this.router.navigate([this.path.members.root, this.path.members.clients], {
      queryParams: { dependentId: dependentId }
    });
  }

  setStudentDetail(student: IdNameModel | null): void {
    this.selectedDependentId = student?.id ?? 0;
    this.selectedUserId = student?.accountManagerId ?? 0;
    this.getBillHistoryDetails();
  }

  getFilterParams() {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      UserId: this.currentUser?.userRoleId === this.constants.roleIds.CLIENT ? this.currentUser?.userId : this.selectedUserId,
      dependentInformationId: this.selectedDependentId,
      CreatedStartDate: moment(this.filters.startDateFilter).format(this.constants.dateFormats.yyyy_MM_DD),
      CreatedEndDate: moment(this.filters.endDateFilter).format(this.constants.dateFormats.yyyy_MM_DD)
    });
  }

  getBillHistoryDetails(): void {
    if (!this.filters.endDateFilter) {
      return;
    }
    this.showPageLoader = true;
    this.paymentService
      .getListWithFilters<CBResponse<BillHistoryRes>>(this.getFilterParams(), `${API_URL.payment.getAllTransactionsOfUser}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<BillHistoryRes>) => {
          this.billHistoryDetails = res.result.items;
          this.totalCount = res.result.totalCount;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }
}
