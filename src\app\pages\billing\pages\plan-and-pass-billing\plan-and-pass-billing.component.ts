import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CommonModule } from '@angular/common';
import { DirectivesModule } from 'src/app/shared/directives/directives.module';
import { ActivatedRoute, Router } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { PlanSummaryService } from 'src/app/pages/settings/pages/plan/services';
import { OpenBillComponent } from './open-bill/open-bill.component';
import { BillHistoryComponent } from './bill-history/bill-history.component';
import { PaymentMethodsComponent } from 'src/app/shared/components/payment-methods/payment-methods.component';
import { CommonUtils } from 'src/app/shared/utils';
import { DependentService } from 'src/app/pages/profile/services';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { CBGetResponse, IdNameModel } from 'src/app/shared/models';
import { DependentInformations } from 'src/app/pages/members/pages/students/models';
import { AuthService } from 'src/app/auth/services';


const DEPENDENCIES = {
  MODULES: [CommonModule, DirectivesModule, MatIconModule],
  COMPONENTS: [PaymentMethodsComponent, OpenBillComponent, BillHistoryComponent]
};

@Component({
  selector: 'app-plan-and-pass-billing',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './plan-and-pass-billing.component.html',
  styleUrl: './plan-and-pass-billing.component.scss'
})
export class PlanAndPassBillingComponent extends BaseComponent implements OnInit {
  selectedTabOption!: string;
  pageTabOptions = { OPEN_BILL: 'Open Bill', BILL_HISTORY: 'Bill History' };
  paymentMethodTab = 'Payment Method';
  studentList!: Array<IdNameModel>;

  constructor(
    protected readonly planSummaryService: PlanSummaryService,
    protected readonly schedulerService: SchedulerService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly dependentService: DependentService,
    private readonly authService: AuthService,
    private readonly router: Router,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.getCurrentUser();
    this.setActiveTabFromQueryParams();
  }

  setActiveTabFromQueryParams(): void {
    this.activatedRoute.queryParams
      .subscribe((params: any) => {
        if (Object.keys(params).length) {
          this.selectedTabOption = params.activeTab;
          return;
        }
        this.setActiveTabOption(this.pageTabOptions.OPEN_BILL);
      });
  }

  setActiveTabOption(tabName: string): void {
    this.selectedTabOption = tabName;
    this.router.navigate([this.path.billing.root, this.path.billing.planAndPass], {
      queryParams: {
        activeTab: tabName
      }
    });
  }

  getCurrentUser(): void {
    this.authService
      .getCurrentUser()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.currentUser = res;
          this.getStudents();
          this.cdr.detectChanges();
        }
      });
  }

  getFilterParamsForStudent(): void {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      InstructorFilter: this.currentUser?.userRoleId === this.constants.roleIds.INSTRUCTOR ? [this.currentUser?.dependentId] : [],
      locationFilter: []
    });
  }

  getStudents(): void {
    this.dependentService
      .add(this.getFilterParamsForStudent(), API_URL.dependentInformations.getAllStudents)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<DependentInformations[]>) => {
          this.studentList = res.result.map(item => ({
            id: item.id,
            accountManagerId: item.accountManagerId,
            name: `${item.firstName} ${item.lastName}`,
            age: item.age
          }));
          this.cdr.detectChanges();
        }
      });
  }

  keepOriginalOrder = () => 0;
}
